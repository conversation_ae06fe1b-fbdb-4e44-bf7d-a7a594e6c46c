// 新版本API地址 - 指向 JY-MONGO 后端
const pubUrl = "http://localhost:4000/api"; // 开发环境
// const pubUrl = "https://your-production-domain.com/api"; // 生产环境

const http = (options) => {
  const token = wx.getStorageSync("tokenKey");

  return new Promise((resolve, reject) => {
    wx.request({
      url: pubUrl + options.url,
      method: options.method || "get",
      data: options.data || {},
      header: {
        "Content-Type": "application/json",
        Authorization: token ? "Bearer " + token : "",
      },
      success: (res) => {
        // 处理响应数据 - 接受所有2xx状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res);
        } else if (res.statusCode === 401) {
          console.warn("⚠️ 401未授权错误");
          // 未授权，清除token并跳转到登录页
          wx.removeStorageSync("tokenKey");
          wx.showToast({
            title: "登录已过期，请重新登录",
            icon: "none",
          });
          wx.redirectTo({
            url: "/pages/login/login",
          });
          reject({
            ...res,
            errorType: "UNAUTHORIZED",
            errorMessage: "登录已过期",
          });
        } else {
          reject({
            ...res,
            errorType: "HTTP_ERROR",
            errorMessage: `HTTP错误: ${res.statusCode}`,
          });
        }
      },
      fail: (error) => {
        reject({
          ...error,
          errorType: "NETWORK_ERROR",
          errorMessage: error.errMsg || "网络请求失败",
        });
      },
    });
  });
};

export default http;
