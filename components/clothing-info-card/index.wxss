/* 卡片容器 */
.clothing-card {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.clothing-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.card-container {
  padding: 24rpx;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.clothing-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

/* 卡片内容 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* OEM标识 */
.oem-badge {
  margin-bottom: 12rpx;
}

/* 供应商信息 */
.supplier-info {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.supplier-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.supplier-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 标签行 */
.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.tag-item {
  margin: 0 !important;
}

.card-box {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.card-content-a {

  font-size: 50rpx;
  font-weight: bold;
}

.card-box-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.card-box-card-tag {
  display: flex;
  flex-direction: row;
  justify-content:left;
  align-items: center;
}

.card-tag{
  margin-right: 10rpx;
  margin-bottom: 20rpx;
}

/* 数量信息 */
.quantity-info {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 1rpx solid #dee2e6;
}

.quantity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.quantity-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.quantity-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}