<view class="clothing-card" bindtap="onCardTap">
  <!-- 普通服装卡片 -->
  <view wx:if="{{!isOem}}" class="card-container">
    <!-- 服装属性标签 -->
    <view class="card-box-content">
      <view class="card-content-a">{{clothingInfo.clothing_name}}</view>
      <van-tag wx:if="{{clothingInfo.img.length>0}}" bindtap="showImg" size="large" color="#986" type="warning">
        图片
      </van-tag>
      <view class="card-content-b">
        <van-tag color="#98281e99" wx:if="{{clothingInfo.price}}" type="primary" size="large" class="card-tag">
          ￥{{clothingInfo.price}}
        </van-tag>
      </view>

    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{clothingInfo.supplier}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{clothingInfo.supplier}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.group_classification}}" wx:for="{{clothingInfo.group_classification}}" wx:key="index" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{item}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.style}}" size="medium" type="success" color="#2775b699" class="card-tag">
        {{clothingInfo.style}}
      </van-tag>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{clothingInfo.long_or_short_sleeve}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.long_or_short_sleeve}}
      </van-tag>
      <van-tag wx:if="{{clothingInfo.size}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.size}}
      </van-tag>

      <van-tag wx:if="{{clothingInfo.pocket_type}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{clothingInfo.pocket_type}}
      </van-tag>
    </view>

    <!-- 数量信息 -->
    <view class="quantity-info">
      <view class="quantity-item">
        <text class="quantity-label">裁剪数</text>
        <text class="quantity-value">{{clothingInfo.clipping_pcs || 0}}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">发货数</text>
        <text class="quantity-value">{{clothingInfo.shipments || 0}}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">到货数</text>
        <text class="quantity-value">{{clothingInfo.arrivalQuantity || 0}}</text>
      </view>
    </view>


  </view>

  <!-- OEM服装卡片 -->
  <view wx:else class="card-container">
    <view class="card-box-content">
      <view class="card-content-a">{{oemClothingInfo.oem_clothing_name}}</view>
      <van-tag wx:if="{{oemClothingInfo.img.length>0}}" bindtap="showImg" size="large" color="#986" type="warning">
        图片
      </van-tag>
      <view class="card-content-b">
        <van-tag color="#98281e99" wx:if="{{oemClothingInfo.price}}" type="primary" size="large" class="card-tag">
          ￥{{oemClothingInfo.price}}
        </van-tag>
      </view>

    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{oemClothingInfo.oem_supplier}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{oemClothingInfo.oem_supplier}}
      </van-tag>
      <van-tag wx:if="{{oemClothingInfo.classification}}" size="medium" type="primary" color="#2775b699" class="card-tag">
        {{oemClothingInfo.classification}}
      </van-tag>
      <van-tag wx:if="{{oemClothingInfo.style}}" size="medium" type="success" color="#2775b699" class="card-tag">
        {{oemClothingInfo.style}}
      </van-tag>
    </view>
    <view class="card-box-card-tag">
      <van-tag wx:if="{{oemClothingInfo.size}}" size="medium" type="success" color="#45b78799" class="card-tag">
        {{oemClothingInfo.size}}
      </van-tag>

    </view>

    <!-- 数量信息 -->
    <view class="quantity-info">
      <view class="quantity-item">
        <text class="quantity-label">进货数</text>
        <text class="quantity-value">{{oemClothingInfo.in_pcs || 0}}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">发货数</text>
        <text class="quantity-value">{{oemClothingInfo.shipments || 0}}</text>
      </view>
      <view class="quantity-item">
        <text class="quantity-label">到货数</text>
        <text class="quantity-value">{{oemClothingInfo.arrivalQuantity || 0}}</text>
      </view>
    </view>
  </view>
</view>