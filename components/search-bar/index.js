// components/search-bar/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示年份步进器
    showYearStepper: {
      type: Boolean,
      value: false
    },
    // 年份值
    year: {
      type: Number,
      value: new Date().getFullYear()
    },
    // 搜索值
    searchValue: {
      type: String,
      value: ''
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请输入搜索内容'
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: true
    },
    // 操作按钮文本
    actionText: {
      type: String,
      value: '搜索'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 年份改变
    onYearChange(e) {
      this.triggerEvent('yearChange', e.detail);
    },

    // 搜索输入
    onSearchInput(e) {
      this.triggerEvent('searchInput', e.detail);
    },

    // 搜索
    onSearch(e) {
      this.triggerEvent('search', {
        value: this.properties.searchValue,
        year: this.properties.year
      });
    }
  }
})
