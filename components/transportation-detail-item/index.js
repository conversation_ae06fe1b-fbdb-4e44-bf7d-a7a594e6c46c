// components/transport-list/index.js
import Api from "../../utils/api.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    detail: Object,
  },

  /**
   * 组件的初始数据
   */
  data: {
    taplock: false, //单击锁，当此值为false时，单击不生效
    longtap: false, //是否触发了长按
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //获取款号信息
    async onGoToDetail(e) {
      if (this.data.taplock) {
        const clothing = e.currentTarget.dataset;
        let imgList = [];
        let params = {
          clothing_id: clothing.clothing_id,
        };
        if (clothing.oem === "是") {
          await Api.getOemClothingInfo(params).then((res) => {
            const info = res.data.data.new_list[0];
            info.oem = "是";
            const percent = (info.shipments / info.in_pcs) * 550;
            if (percent >= 550) {
              e.percent = 550;
            }
            if (percent <= 190) {
              e.percent = 190;
            }
            if (percent > 190 && percent < 550) {
              e.percent = percent.toFixed(0);
            }
            if (info.img) {
              info.img.forEach((element) => {
                imgList.push(element.url);
              });
              info.imgList = imgList;
            }
            this.triggerEvent("monitor", info);
          });
        } else {
          await Api.getClothingInfo(params).then((res) => {
            const info = res.data.data.new_list[0];
            const percent = (info.shipments / info.clipping_pcs) * 550;
            if (percent >= 550) {
              e.percent = 550;
            }
            if (percent <= 190) {
              e.percent = 190;
            }
            if (percent > 190 && percent < 550) {
              e.percent = percent.toFixed(0);
            }
            if (info.img) {
              info.img.forEach((element) => {
                imgList.push(element.url);
              });
              info.imgList = imgList;
            }
            this.triggerEvent("monitor", info);
          });
        }

        // this.setData({
        //   show: true,
        // });
      }
    },
    // 长按事件
    longtap(e) {
      const list = this.properties.detail.bag
      const clothing_id = e.currentTarget.dataset.clothing_id
      const obj = list.find(e => e.clothing_id == clothing_id)

      wx.previewImage({
        urls: obj.img,
      });
      this.setData({
        taplock: false,
        longtap: true
      });
    },
    // 触摸开始
    touchStart() {
      this.setData({
        taplock: true,
        longtap: false
      });

    },
    // 触摸结束
    touchEnd() {
      if (this.data.longtap) {} else
        this.setData({
          taplock: true,
        });

    },
  },
});