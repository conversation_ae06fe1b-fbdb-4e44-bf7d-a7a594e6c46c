// components/transportation/index.js
import Api from "../../utils/api.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    transportation: Object,
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: "",
    show: false,
    transportation_id: "",
  },

  lifetimes: {
    attached() {
      this.setData({
        price: this.properties.transportation.price,
      });
      // 在组件实例进入页面节点树时执行
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //显示原图
    showImg() {
      const imgList = [];
      for (
        let i = 0;
        i < this.properties.transportation.transportation_img.length;
        i++
      ) {
        imgList.push(this.properties.transportation.transportation_img[i].url);
      }
      wx.previewImage({
        urls: imgList,
      });
    },
    showPopup() {
      console.log("showPopup", this.properties.transportation);
      this.setData({
        show: true,
        transportation_id: this.properties.transportation.transportation_id,
      });
    },

    cancel() {
      this.setData({
        show: false,
      });
    },

    async changePrice() {
      const price = this.data.price;
      const transportation_id = this.data.transportation_id;
      let params = {};
      params.price = price;
      params.transportation_id = transportation_id;
      const res = await Api.changePrice(params);
      if (res.statusCode == 500) {
        this.setData({
          price: "",
        });
      }
      this.setData({
        show: false,
      });
    },

    clickOverlay() {
      var myEventDetail = {
        val: false,
      }; // detail对象，提供给事件监听函数
      this.triggerEvent("myevent", myEventDetail); //myevent自定义名称事件，父组件中使用
    },
  },
});
