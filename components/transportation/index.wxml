<!-- components/transportation/index.wxml -->
<!-- 使用 Vant 卡片组件 -->
<van-card
  title="{{transportation.supplier || transportation.transportation_company}}"
  desc="{{transportation.total_package_quantity || transportation.total_pcs || 0}}包"
  thumb="{{transportation.transportation_img && transportation.transportation_img[0] ? transportation.transportation_img[0].url : ''}}"
  bind:click="onCardClick"
>
  <!-- 标签区域 -->
  <view slot="tags" class="tags-container">
    <van-tag wx:if="{{transportation.weight}}" type="primary" size="small">
      {{transportation.weight}} kg
    </van-tag>
    <van-tag wx:if="{{price}}" type="success" size="small">
      $ {{price}}
    </van-tag>
    <van-tag wx:if="{{!price}}" type="warning" size="small" bind:click="showPopup">
      设置单价
    </van-tag>
  </view>

  <!-- 底部信息 -->
  <view slot="footer" class="footer-info">
    <view class="date-info">
      <van-tag type="default" size="small">出货: {{transportation.date_out}}</van-tag>
      <van-tag type="primary" size="small">{{transportation.days}}天</van-tag>
      <van-tag wx:if="{{transportation.date_arrived}}" type="success" size="small">
        到货: {{transportation.date_arrived}}
      </van-tag>
    </view>
  </view>
</van-card>

<!-- 使用 Vant 弹出层设置价格 -->
<van-popup
  show="{{show}}"
  position="center"
  round
  bind:close="cancel"
  custom-style="width: 80%;"
>
  <view class="price-popup">
    <view class="popup-header">
      <text class="popup-title">设置运费单价</text>
    </view>

    <view class="popup-body">
      <van-field
        value="{{inputPrice}}"
        type="number"
        placeholder="请输入运费单价"
        bind:change="onPriceInput"
        border="{{false}}"
      />
    </view>

    <view class="popup-footer">
      <van-button
        type="default"
        size="small"
        bind:click="cancel"
        custom-style="margin-right: 10px;"
      >
        取消
      </van-button>
      <van-button
        type="primary"
        size="small"
        bind:click="changePrice"
      >
        确定
      </van-button>
    </view>
  </view>
</van-popup>