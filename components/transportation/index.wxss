/* components/transportation/index.wxss */
.card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.transportation {
  display: flex;
  flex-direction: column;
}

.detail {
  margin-bottom: 20rpx;
}

.a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.supplier {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.b {
  display: flex;
  gap: 10rpx;
}

.weight-tag,
.price-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.weight-tag {
  background-color: rgba(104, 94, 72, 0.6);
}

.price-tag {
  background-color: rgba(152, 40, 30, 0.6);
}

.price-tag.clickable {
  background-color: rgba(152, 40, 30, 0.8);
}

.total_package_quantity {
  font-size: 28rpx;
  color: #666;
}

.date {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.date-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.out-date {
  background-color: rgba(39, 117, 182, 0.6);
}

.days {
  background-color: rgba(244, 122, 85, 0.6);
}

.arrived-date {
  background-color: rgba(69, 183, 135, 0.6);
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-price {
  width: 640rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.popup-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-body {
  padding: 40rpx 30rpx;
}

.input-price {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 32rpx;
  text-align: center;
}

.input-placeholder {
  color: #999;
}

.popup-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 28rpx;
  border: none;
  background-color: transparent;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #eee;
}

.confirm-btn {
  color: #07c160;
  font-weight: bold;
}
