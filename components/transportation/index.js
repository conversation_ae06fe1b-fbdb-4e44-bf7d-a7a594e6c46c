// components/transportation/index.js
import Api from "../../utils/api.js";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    transportation: {
      type: Object,
      value: {}
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    price: "",
    show: false,
    transportation_id: "",
    inputPrice: ""
  },

  lifetimes: {
    attached() {
      this.setData({
        price: this.properties.transportation.price || ""
      });
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 卡片点击事件
    onCardClick() {
      // 触发父组件的点击事件
      this.triggerEvent('cardClick', this.properties.transportation);
    },

    showPopup() {
      this.setData({
        show: true,
        transportation_id: this.properties.transportation._id || this.properties.transportation.transportation_id,
        inputPrice: this.data.price
      });
    },

    cancel() {
      this.setData({
        show: false,
        inputPrice: ""
      });
    },

    // 价格输入事件 - 适配 Vant Field 组件
    onPriceInput(e) {
      this.setData({
        inputPrice: e.detail
      });
    },

    async changePrice() {
      try {
        const price = this.data.inputPrice;
        const transportation_id = this.data.transportation_id;

        if (!price || !transportation_id) {
          wx.showToast({
            title: "请输入有效价格",
            icon: "none"
          });
          return;
        }

        // 这里可以调用价格更新API
        // const params = { price, transportation_id };
        // const res = await Api.changePrice(params);

        // 更新本地价格显示
        this.setData({
          price: price,
          show: false,
          inputPrice: ""
        });

        wx.showToast({
          title: "价格更新成功",
          icon: "success"
        });
      } catch (error) {
        console.error("更新价格失败:", error);
        wx.showToast({
          title: "更新失败",
          icon: "none"
        });
      }
    },

    clickOverlay() {
      const myEventDetail = {
        val: false
      };
      this.triggerEvent('myevent', myEventDetail);
    },
  },
});
