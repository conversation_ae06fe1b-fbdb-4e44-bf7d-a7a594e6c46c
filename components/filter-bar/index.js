// components/filter-bar/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示公司筛选
    showCompanyFilter: {
      type: Boolean,
      value: false
    },
    // 是否显示状态筛选
    showStatusFilter: {
      type: Boolean,
      value: false
    },
    // 是否显示年份筛选
    showYearFilter: {
      type: Boolean,
      value: false
    },
    // 公司选项
    companyOptions: {
      type: Array,
      value: []
    },
    // 状态选项
    statusOptions: {
      type: Array,
      value: []
    },
    // 年份选项
    yearOptions: {
      type: Array,
      value: []
    },
    // 选中的公司
    selectedCompany: {
      type: String,
      value: ''
    },
    // 选中的状态
    selectedStatus: {
      type: String,
      value: ''
    },
    // 选中的年份
    selectedYear: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 公司选择改变
    onCompanyChange(e) {
      this.triggerEvent('companyChange', e.detail);
    },

    // 状态选择改变
    onStatusChange(e) {
      this.triggerEvent('statusChange', e.detail);
    },

    // 年份选择改变
    onYearChange(e) {
      this.triggerEvent('yearChange', e.detail);
    }
  }
})
