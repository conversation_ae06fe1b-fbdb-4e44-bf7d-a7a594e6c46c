/* components/oemClothing-details/index.wxss */
.popup {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border-left: 8rpx solid #ff6b6b; /* OEM 服装特殊标识 */
}

.card {
  padding: 30rpx;
}

.card-box-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.card-content-a {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.card-content-b {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.price-tag {
  padding: 8rpx 16rpx;
  background-color: rgba(255, 107, 107, 0.6);
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.image-btn {
  padding: 8rpx 16rpx;
  background-color: #ff9500;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.card-box-card-tag {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.tag {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #fff;
}

.supplier-tag,
.classification-tag {
  background-color: rgba(255, 107, 107, 0.6);
}

.two-tag,
.size-tag,
.style-tag {
  background-color: rgba(255, 149, 0, 0.6);
}

.card-in_pcs,
.card-shipments {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  position: relative;
}

.card-shipments {
  background: linear-gradient(
    90deg, 
    rgba(255, 107, 107, 0.1) 0%, 
    rgba(255, 107, 107, 0.1) var(--progress-width, 0%), 
    transparent var(--progress-width, 0%)
  );
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background-color: #f0f0f0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b 0%, #e55a5a 100%);
  transition: width 0.3s ease;
}
