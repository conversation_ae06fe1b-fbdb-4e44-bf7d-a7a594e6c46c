<!-- components/clothing-details/index.wxml -->
<!-- 使用 Vant 卡片组件 -->
<van-card
  title="{{clothingInfo.clothing_name}}"
  desc="{{clothingInfo.clothing_id}}"
  price="{{clothingInfo.clothing_price}}"
  currency="￥"
  thumb="{{clothingInfo.clothing_img && clothingInfo.clothing_img[0] ? clothingInfo.clothing_img[0].url : ''}}"
>
  <!-- 标签区域 -->
  <view slot="tags" class="tags-container">
    <van-tag wx:if="{{clothingInfo.supplier}}" type="primary" size="small">
      {{clothingInfo.supplier}}
    </van-tag>
    <van-tag
      wx:if="{{clothingInfo.group_classification}}"
      wx:for="{{clothingInfo.group_classification}}"
      wx:key="index"
      type="success"
      size="small"
    >
      {{item}}
    </van-tag>
  </view>

  <!-- 属性标签 -->
  <view slot="footer" class="attributes-container">
    <van-tag wx:if="{{clothingInfo.long_or_short_sleeve}}" type="default" size="small">
      {{clothingInfo.long_or_short_sleeve}}
    </van-tag>
    <van-tag wx:if="{{clothingInfo.size}}" type="default" size="small">
      {{clothingInfo.size}}
    </van-tag>
    <van-tag wx:if="{{clothingInfo.style}}" type="default" size="small">
      {{clothingInfo.style}}
    </van-tag>
    <van-tag wx:if="{{clothingInfo.pocket_type}}" type="default" size="small">
      {{clothingInfo.pocket_type}}
    </van-tag>

    <!-- 图片按钮 -->
    <van-button
      wx:if="{{clothingInfo.clothing_img && clothingInfo.clothing_img.length > 0}}"
      type="primary"
      size="mini"
      bind:click="showImg"
    >
      查看图片
    </van-button>
  </view>
</van-card>

<!-- 数量信息 -->
<view class="quantity-info">
  <van-cell title="裁剪数" value="{{clothingInfo.clipping_pcs || 0}}" border="{{false}}" />

  <van-cell
    wx:if="{{clothingInfo.shipments !== 0 && clothingInfo.shipments !== null}}"
    title="发货数"
    value="{{clothingInfo.shipments || 0}}"
    border="{{false}}"
  >
    <!-- 进度条 -->
    <view slot="right-icon" class="progress-container">
      <van-progress
        percentage="{{(clothingInfo.shipments / clothingInfo.clipping_pcs * 100) || 0}}"
        stroke-width="6"
        color="#1989fa"
        track-color="#f2f3f5"
      />
    </view>
  </van-cell>
</view>
