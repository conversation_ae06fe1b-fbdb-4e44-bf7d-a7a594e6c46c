// pages/transportations/transportations.js
import Api from "../../utils/api.js";

let currentPage = 1; // 当前第几页，从1开始
let pageSize = 10; // 每页显示多少数据
let transportation_company = ""; // 货运公司
let arrived = ""; // 到货状态
let clockPopup = false;

Page({
  data: {
    // Vant 下拉菜单数据格式
    transportationCompanyOptions: [],
    statusOptions: [
      {
        text: "全部",
        value: "",
      },
      {
        text: "未到货",
        value: "未到货",
      },
      {
        text: "已到货",
        value: "已到货",
      },
    ],
    selectedCompany: "",
    selectedStatus: "",
    loadMore: false, // "上拉加载"的变量，默认false，隐藏
    loadAll: false, // "没有数据"的变量，默认false，隐藏
    list: [],
    show: false,
    showDate: false,
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      return value;
    },
    transportationDetail: {},
    checked: false,
    currentDate: new Date().getTime(),
    showClothingInfo: false,
    clothingInfo: {},
    oemClothingInfo: {},
    loading: false,
  },

  onLoad: function (options) {
    this.getTransportationList();
    this.getTransportationCompanyList();
  },

  onReachBottom: function () {
    let that = this;
    if (!that.data.loadMore && !that.data.loadAll) {
      that.setData({
        loadMore: true, // 加载中
        loadAll: false, // 是否加载完所有数据
      });
      // 加载更多，这里做下延时加载
      setTimeout(function () {
        that.getTransportationList();
      }, 1000);
    }
  },

  // 获取货运单列表
  async getTransportationList() {
    let that = this;

    try {
      const params = {
        page: currentPage,
        limit: pageSize,
        transportation_company: transportation_company,
      };

      // 根据到货状态筛选
      if (arrived === "未到货") {
        // 这里可以添加未到货的筛选逻辑
      } else if (arrived === "已到货") {
        // 这里可以添加已到货的筛选逻辑
      }

      const res = await Api.getTransportationList(params);
      console.log("getTransportationList666", res);

      if (res.data && res.data.data && res.data.data.length > 0) {
        currentPage++;
        const list = res.data.data;

        // 调试：查看第一个货运单对象的结构
        if (list.length > 0) {
          console.log("第一个货运单对象:", list[0]);
          console.log("第一个货运单的所有属性:", Object.keys(list[0]));
        }

        // 处理日期格式和计算天数
        list.forEach((element) => {
          if (element.date_out) {
            element.date_out = that.formatDate(element.date_out);
          }
          if (element.date_arrived) {
            element.date_arrived = that.formatDate(element.date_arrived);
            element.days = that.calculateDays(
              element.date_arrived,
              element.date_out
            );
          } else {
            element.days = that.calculateDays(new Date(), element.date_out);
          }
        });

        let new_list = currentPage === 2 ? list : that.data.list.concat(list);
        that.setData({
          list: new_list,
          loadMore: false,
        });

        if (list.length < pageSize) {
          that.setData({
            loadMore: false,
            loadAll: true,
          });
        }
      } else {
        that.setData({
          loadAll: true,
          loadMore: false,
        });
      }
    } catch (error) {
      console.error("获取货运单列表失败:", error);
      wx.showToast({
        title: "获取数据失败",
        icon: "none",
      });
      that.setData({
        loadMore: false,
      });
    }
  },

  // 获取货运公司列表
  async getTransportationCompanyList() {
    try {
      const res = await Api.getTransportationCompanyList();

      if (res.data && res.data.code === 200) {
        let list = [
          {
            text: "全部公司",
            value: "",
          },
        ];

        res.data.data.forEach((element) => {
          list.push({
            text: element,
            value: element,
          });
        });

        this.setData({
          transportationCompanyOptions: list,
        });
      }
    } catch (error) {
      console.error("获取货运公司列表失败:", error);
    }
  },

  // 货运公司选择改变 - 适配 Vant 组件
  onCompanyChange(e) {
    const selectedValue = e.detail;

    this.setData({
      selectedCompany: selectedValue,
      list: [],
    });
    currentPage = 1;
    transportation_company = selectedValue;
    this.getTransportationList();
  },

  // 到货状态选择改变 - 适配 Vant 组件
  onStatusChange(e) {
    const selectedValue = e.detail;

    this.setData({
      selectedStatus: selectedValue,
      list: [],
    });
    currentPage = 1;
    arrived = selectedValue;
    this.getTransportationList();
  },

  // 阻止冒泡
  onGetCode(e) {
    clockPopup = true;
    setTimeout(() => {
      clockPopup = false;
    }, 200);
  },

  // 格式化日期
  formatDate(date) {
    if (!date) return "";
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  },

  // 计算天数差
  calculateDays(endDate, startDate) {
    if (!endDate || !startDate) return 0;
    const end = new Date(endDate);
    const start = new Date(startDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  },

  // 打开货运单详情页
  async onGoToDetail(e) {
    // 获取点击的货运单数据
    const transportationItem = e.currentTarget.dataset.item;
    console.log("transportationItem:", transportationItem);

    if (!transportationItem) {
      wx.showToast({
        title: "获取货运单数据失败",
        icon: "none",
      });
      return;
    }

    // 尝试多种可能的ID字段名
    const transportationId =
      transportationItem.transportation_id ||
      console.log("提取的ID:", transportationId);
    console.log("所有可能的ID字段:", {
      _id: transportationItem._id,
      transportation_id: transportationItem.transportation_id,
      id: transportationItem.id,
    });

    if (!transportationId) {
      wx.showToast({
        title: "获取货运单ID失败",
        icon: "none",
      });
      console.log("货运单对象的所有属性:", Object.keys(transportationItem));
      return;
    }

    wx.navigateTo({
      url: `/pages/transportation-detail/transportation-detail?id=${transportationId}`,
    });
  },

  // 关闭货运单详情弹窗
  onClose() {
    this.setData({
      show: false,
      transportationDetail: {},
    });
  },

  // 时间选择
  onInput(event) {
    this.setData({
      currentDate: event.detail,
    });
  },

  // 时间选择后确认
  async onConfirm(event) {
    try {
      const selectedDate = this.formatDate(event.detail);
      this.setData({
        showDate: false,
      });

      // 更新到货日期
      const params = {
        id: this.data.transportationDetail._id,
        arrived_date: selectedDate,
      };

      const res = await Api.updateArrivedDate(params);

      if (res.data && res.data.code === 200) {
        // 更新本地列表数据
        const newList = this.data.list.map((element) => {
          if (element._id === params.id) {
            element.date_arrived = selectedDate;
            element.days = this.calculateDays(selectedDate, element.date_out);
          }
          return element;
        });

        this.setData({
          list: newList,
          checked: true,
        });

        wx.showToast({
          title: "更新成功",
          icon: "success",
        });
      } else {
        wx.showToast({
          title: "更新失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("更新到货日期失败:", error);
      wx.showToast({
        title: "更新失败",
        icon: "none",
      });
    }
  },

  // 时间选择取消
  onCancel() {
    this.setData({
      showDate: false,
      checked: false,
    });
  },

  // 关闭日期选择器
  onCloseDatePopup() {
    this.setData({
      showDate: false,
      checked: false,
    });
  },

  // Switch 开关改变
  onChangeWitch({ detail }) {
    const content = detail ? "确认到货？" : "确认未到货？";

    wx.showModal({
      title: "提示",
      content: content,
      success: (res) => {
        if (res.confirm) {
          if (detail) {
            this.setData({
              checked: detail,
              showDate: true,
            });
          } else {
            this.changeTransportStatus(detail);
            this.setData({
              checked: detail,
            });
          }
        }
      },
    });
  },

  // 改变货运单状态为未到货
  async changeTransportStatus(detail) {
    if (!detail) {
      try {
        const params = {
          id: this.data.transportationDetail._id,
          arrived_date: null,
        };

        const res = await Api.updateArrivedDate(params);

        if (res.data && res.data.code === 200) {
          // 更新本地列表数据
          const newList = this.data.list.map((element) => {
            if (element._id === params.id) {
              element.date_arrived = null;
              element.days = this.calculateDays(new Date(), element.date_out);
            }
            return element;
          });

          this.setData({
            list: newList,
          });

          wx.showToast({
            title: "更新成功",
            icon: "success",
          });
        }
      } catch (error) {
        console.error("更新货运状态失败:", error);
        wx.showToast({
          title: "更新失败",
          icon: "none",
        });
      }
    }
  },

  /**
   * 更新单个货运项的数据（由货运详情页面调用）
   */
  updateTransportationItem(transportationId, arrivedDate) {
    try {
      console.log("更新货运项数据:", { transportationId, arrivedDate });

      const newList = this.data.list.map((element) => {
        if (
          element._id === transportationId ||
          element.transportation_id === transportationId
        ) {
          // 更新到货日期
          element.date_arrived = arrivedDate;

          // 重新计算天数
          if (arrivedDate) {
            element.days = this.calculateDays(arrivedDate, element.date_out);
          } else {
            element.days = this.calculateDays(new Date(), element.date_out);
          }

          console.log("更新后的货运项:", element);
        }
        return element;
      });

      this.setData({
        list: newList,
      });

      console.log("货运列表数据已更新");
    } catch (error) {
      console.error("更新货运项数据失败:", error);
    }
  },

  // 监听服装详情事件
  monitor(e) {
    const { clothing_id, oem, item } = e.detail;

    if (oem) {
      // 显示 OEM 服装详情
      this.setData({
        showClothingInfo: true,
        oemClothingInfo: item,
        clothingInfo: {},
      });
    } else {
      // 显示普通服装详情
      this.setData({
        showClothingInfo: true,
        clothingInfo: item,
        oemClothingInfo: {},
      });
    }
  },

  // 关闭服装信息弹窗
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      clothingInfo: {},
      oemClothingInfo: {},
    });
  },
});
