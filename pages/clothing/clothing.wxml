<!-- 搜索区域容器 -->
<view class="search-container {{hasSearchResults ? 'search-moved-up' : ''}}">
  <!-- 使用自定义搜索栏组件 -->
  <z-search-bar
    showYearStepper="{{true}}"
    year="{{year}}"
    minYear="2020"
    maxYear="2030"
    searchValue="{{search}}"
    placeholder="搜索款号"
    showAction="{{true}}"
    actionText="搜索"
    disabled="{{loading}}"
    bind:yearChange="onYearChange"
    bind:searchInput="onSearchInput"
    bind:search="onGoToSearch"
  ></z-search-bar>
  
  <!-- 示例数据控制按钮 -->
  <view class="demo-controls" wx:if="{{!hasSearchResults}}">
    <van-button 
      size="small" 
      type="primary" 
      bindtap="loadSampleData"
      style="margin-right: 20rpx;">
      加载示例数据
    </van-button>
  </view>
  
  <view class="demo-controls" wx:if="{{hasSearchResults && search === '示例数据'}}">
    <van-button 
      size="small" 
      type="warning" 
      bindtap="clearSampleData">
      切换到真实搜索
    </van-button>
  </view>
</view>

<!-- 搜索结果容器 -->
<view class="search-results {{hasSearchResults ? 'show-results' : ''}}">
  <!-- 普通服装列表 -->
  <view wx:for="{{clothingList}}" wx:key="index" class="clothing-item">
    <clothing-info-card 
      clothingInfo="{{item}}" 
      isOem="{{false}}"
      bind:cardTap="onClothingCardTap"
      bind:changePrice="onChangePrice">
    </clothing-info-card>
  </view>

  <!-- OEM服装列表 -->
  <view wx:for="{{oemClothingList}}" wx:key="index" class="clothing-item">
    <clothing-info-card 
      oemClothingInfo="{{item}}" 
      isOem="{{true}}"
      bind:cardTap="onOemClothingCardTap"
      bind:changePrice="onChangePrice">
    </clothing-info-card>
  </view>
</view>

<!-- 空状态提示 -->
<view wx:if="{{!hasSearchResults && !loading}}" class="empty-container">
  <van-empty
    description="暂无搜索结果"
    image="search">
    <view slot="bottom" class="empty-actions">
      <van-button 
        type="primary" 
        size="small" 
        bindtap="loadSampleData"
        style="margin-right: 20rpx;">
        查看示例数据
      </van-button>
      <van-button 
        type="default" 
        size="small" 
        bindtap="onGoToSearch">
        开始搜索
      </van-button>
    </view>
  </van-empty>
</view>

<!-- 使用自定义加载状态组件 -->
<z-loading-state
  loading="{{loading}}"
  loadAll="{{false}}"
  loadingText="搜索中..."
></z-loading-state>
