/* 搜索区域 */
.search-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  position: relative;
  z-index: 10;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center top;
}

/* 搜索区域上移状态 */
.search-container.search-moved-up {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 示例数据控制按钮 */
.demo-controls {
  display: flex;
  justify-content: center;
  margin-top: 20rpx;
  padding: 10rpx 0;
}

.stepper-container {
  display: flex;
  justify-content: center;
}

.stepper-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  overflow: hidden;
}

.stepper-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #356363;
  color: #fff;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepper-value {
  width: 100rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background-color: #fff;
}

.search-wrapper {
  display: flex;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  padding: 20rpx 30rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn[disabled] {
  background-color: rgba(7, 193, 96, 0.5);
}

/* 搜索结果容器 */
.search-results {
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  padding-top: 0;
}

.search-results.show-results {
  opacity: 1;
  transform: translateY(0);
  padding-top: 140rpx; /* 为固定的搜索栏留出空间 */
}

/* 服装项 */
.clothing-item {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(30rpx);
}

.clothing-item:nth-child(1) { animation-delay: 0.1s; }
.clothing-item:nth-child(2) { animation-delay: 0.2s; }
.clothing-item:nth-child(3) { animation-delay: 0.3s; }
.clothing-item:nth-child(4) { animation-delay: 0.4s; }
.clothing-item:nth-child(5) { animation-delay: 0.5s; }
.clothing-item:nth-child(n+6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态 */
.empty-container {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-hint {
  font-size: 24rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}
